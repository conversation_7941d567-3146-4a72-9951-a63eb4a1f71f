import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { API_URL } from './api';

const DriverAuth: React.FC<{ onLogin: (token: string) => void }> = ({ onLogin }) => {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [message, setMessage] = useState('');
  const navigate = useNavigate();

  const login = async () => {
    setMessage('');
    try {
      const params = new URLSearchParams();
      params.append('username', username);
      params.append('password', password);
      const res = await fetch(`${API_URL}/driver/token`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        body: params
      });
      const data = await res.json();

      if (res.ok && data.access_token) {
        setMessage('Login successful!');
        onLogin(data.access_token);
        // Navigation is handled by the onLogin callback in App.tsx
      } else {
        setMessage(data.detail || 'Login failed');
      }
    } catch (error) {
      setMessage('Network error. Please try again.');
      console.error('Driver login error:', error);
    }
  };

  return (
    <div className="panel">
      <h2>Driver Login</h2>
      <div className="auth-toggle">
        <button disabled>Login</button>
      </div>
      <div>
        <h3>Login</h3>
        <input placeholder="Driver Username" value={username} onChange={e => setUsername(e.target.value)} />
        <input placeholder="Driver Password" type="password" value={password} onChange={e => setPassword(e.target.value)} />
        <button onClick={login}>Login Driver</button>
      </div>
      {message && <p className="message">{message}</p>}
    </div>
  );
};

export default DriverAuth;
