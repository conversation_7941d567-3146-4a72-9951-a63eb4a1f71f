import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { API_URL } from './api';

const UserAuth: React.FC<{ onLogin: (token: string) => void }> = ({ onLogin }) => {
  const [authPage, setAuthPage] = useState<'login' | 'register'>('login');
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [message, setMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const navigate = useNavigate();

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      if (authPage === 'register') {
        register();
      } else {
        login();
      }
    }
  };

  const register = async () => {
    setMessage('');
    setIsLoading(true);

    // Basic validation
    if (!username.trim() || !password.trim()) {
      setMessage('Please enter both username and password');
      setIsLoading(false);
      return;
    }

    try {
      const res = await fetch(`${API_URL}/register`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ username: username.trim(), password })
      });
      const data = await res.json();

      if (res.ok && data.msg && data.msg.includes('success')) {
        setMessage('Registration successful! Logging you in...');
        // Automatically log in the user after successful registration
        await performLogin();
      } else {
        setMessage(data.detail || data.msg || 'Registration failed');
        setIsLoading(false);
      }
    } catch (error) {
      setMessage('Network error. Please try again.');
      console.error('Registration error:', error);
      setIsLoading(false);
    }
  };

  const performLogin = async () => {
    try {
      const params = new URLSearchParams();
      params.append('username', username.trim());
      params.append('password', password);
      const res = await fetch(`${API_URL}/token`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        body: params
      });
      const data = await res.json();

      if (res.ok && data.access_token) {
        setMessage('Login successful!');
        setIsLoading(false);
        onLogin(data.access_token);
      } else {
        setMessage(data.detail || 'Auto-login failed. Please login manually.');
        setIsLoading(false);
        setAuthPage('login');
      }
    } catch (error) {
      setMessage('Auto-login failed. Please login manually.');
      console.error('Auto-login error:', error);
      setIsLoading(false);
      setAuthPage('login');
    }
  };

  const login = async () => {
    setMessage('');
    setIsLoading(true);

    // Basic validation
    if (!username.trim() || !password.trim()) {
      setMessage('Please enter both username and password');
      setIsLoading(false);
      return;
    }

    try {
      const params = new URLSearchParams();
      params.append('username', username.trim());
      params.append('password', password);
      const res = await fetch(`${API_URL}/token`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        body: params
      });
      const data = await res.json();

      if (res.ok && data.access_token) {
        setMessage('Login successful!');
        setIsLoading(false);
        onLogin(data.access_token);
      } else {
        setMessage(data.detail || 'Login failed');
        setIsLoading(false);
      }
    } catch (error) {
      setMessage('Network error. Please try again.');
      console.error('Login error:', error);
      setIsLoading(false);
    }
  };

  return (
    <div className="panel">
      <h2>User Login / Register</h2>
      <div className="auth-toggle">
        <button onClick={() => setAuthPage('login')} disabled={authPage === 'login'}>Login</button>
        <button onClick={() => setAuthPage('register')} disabled={authPage === 'register'}>Register</button>
      </div>
      {authPage === 'register' && (
        <div>
          <h3>Register</h3>
          <input
            placeholder="Username"
            value={username}
            onChange={e => setUsername(e.target.value)}
            onKeyPress={handleKeyPress}
            disabled={isLoading}
          />
          <input
            placeholder="Password"
            type="password"
            value={password}
            onChange={e => setPassword(e.target.value)}
            onKeyPress={handleKeyPress}
            disabled={isLoading}
          />
          <button onClick={register} disabled={isLoading}>
            {isLoading ? 'Processing...' : 'Register'}
          </button>
        </div>
      )}
      {authPage === 'login' && (
        <div>
          <h3>Login</h3>
          <input
            placeholder="Username"
            value={username}
            onChange={e => setUsername(e.target.value)}
            onKeyPress={handleKeyPress}
            disabled={isLoading}
          />
          <input
            placeholder="Password"
            type="password"
            value={password}
            onChange={e => setPassword(e.target.value)}
            onKeyPress={handleKeyPress}
            disabled={isLoading}
          />
          <button onClick={login} disabled={isLoading}>
            {isLoading ? 'Processing...' : 'Login'}
          </button>
        </div>
      )}
      {message && <p className="message">{message}</p>}
    </div>
  );
};

export default UserAuth;
