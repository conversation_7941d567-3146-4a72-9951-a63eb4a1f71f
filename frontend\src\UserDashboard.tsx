import React, { useEffect, useState } from 'react';
import { API_URL } from './api';
import { Button, TextField, Typography, Paper, List, ListItem, ListItemText, Box, Divider } from '@mui/material';
import MapPlaceholder from './MapPlaceholder';
import NotificationBar from './NotificationBar';
import PaymentSection from './PaymentSection';

const UserDashboard: React.FC<{ onLogout?: () => void }> = ({ onLogout }) => {
  const [pickup, setPickup] = useState('');
  const [dropoff, setDropoff] = useState('');
  const [token] = useState(localStorage.getItem('userToken') || '');
  const [bookings, setBookings] = useState<any[]>([]);
  const [message, setMessage] = useState('');
  const [username, setUsername] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [profileMsg, setProfileMsg] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);

  useEffect(() => {
    if (!token) {
      setHasError(true);
      setIsLoading(false);
      return;
    }

    const initializeDashboard = async () => {
      try {
        setIsLoading(true);
        setHasError(false);
        await fetchBookings();
        setUsername('User');
        setIsLoading(false);

        // Poll bookings every 5 seconds
        const interval = setInterval(fetchBookings, 5000);
        return () => clearInterval(interval);
      } catch (error) {
        console.error('Dashboard initialization error:', error);
        setHasError(true);
        setIsLoading(false);
      }
    };

    const cleanup = initializeDashboard();
    return () => {
      if (cleanup && typeof cleanup === 'function') {
        cleanup();
      }
    };
  }, [token]);

  const fetchBookings = async () => {
    try {
      const res = await fetch(`${API_URL}/bookings`, {
        headers: { 'Authorization': `Bearer ${token}` }
      });

      if (res.status === 401) {
        // Token is invalid or expired
        console.log('Token expired, redirecting to login');
        handleLogout();
        return;
      }

      if (!res.ok) {
        console.error('Failed to fetch bookings:', res.status);
        return;
      }

      const data = await res.json();
      if (Array.isArray(data)) {
        setBookings(data);
      } else {
        console.error('Invalid bookings data:', data);
        setBookings([]);
      }
    } catch (error) {
      console.error('Error fetching bookings:', error);
      setBookings([]);
    }
  };

  const bookRide = async () => {
    setMessage('');
    const res = await fetch(`${API_URL}/book?pickup=${pickup}&dropoff=${dropoff}`, {
      method: 'POST',
      headers: { 'Authorization': `Bearer ${token}` }
    });
    const data = await res.json();
    setMessage(data.msg || data.detail || '');
    fetchBookings();
  };

  const handlePasswordChange = async () => {
    setProfileMsg('');
    const res = await fetch(`${API_URL}/user/profile`, {
      method: 'PUT',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(newPassword)
    });
    const data = await res.json();
    setProfileMsg(data.msg || data.detail || '');
    setNewPassword('');
  };

  const handleLogout = () => {
    localStorage.removeItem('userToken');
    if (onLogout) onLogout();
    window.location.href = '/user/login';
  };

  // Show loading state
  if (isLoading) {
    return (
      <Box maxWidth={500} width="100%" mx="auto" mt={4} px={1}>
        <Paper elevation={3} sx={{ p: { xs: 1.5, sm: 3 } }}>
          <Typography variant="h5" gutterBottom>Loading Dashboard...</Typography>
          <Typography>Please wait while we load your data.</Typography>
        </Paper>
      </Box>
    );
  }

  // Show error state
  if (hasError || !token) {
    return (
      <Box maxWidth={500} width="100%" mx="auto" mt={4} px={1}>
        <Paper elevation={3} sx={{ p: { xs: 1.5, sm: 3 } }}>
          <Typography variant="h5" gutterBottom color="error">Authentication Error</Typography>
          <Typography>Your session has expired or is invalid. Please log in again.</Typography>
          <Button variant="contained" onClick={handleLogout} sx={{ mt: 2 }}>
            Go to Login
          </Button>
        </Paper>
      </Box>
    );
  }

  return (
    <Box maxWidth={500} width="100%" mx="auto" mt={4} px={1}>
      <Paper elevation={3} sx={{ p: { xs: 1.5, sm: 3 } }}>
        <Box display="flex" flexDirection={{ xs: 'column', sm: 'row' }} justifyContent="space-between" alignItems={{ xs: 'flex-start', sm: 'center' }}>
          <Typography variant="h5" gutterBottom>User Dashboard</Typography>
          <Button variant="outlined" color="error" onClick={handleLogout} sx={{ mt: { xs: 1, sm: 0 } }}>Logout</Button>
        </Box>
        <NotificationBar message={message} severity={message.includes('success') ? 'success' : 'info'} onClose={() => setMessage('')} />
        <Typography variant="subtitle1">Profile: {username}</Typography>
        <Divider sx={{ my: 2 }} />
        <Typography variant="h6">Book a Ride</Typography>
        <MapPlaceholder height={180} />
        <TextField label="Pickup" value={pickup} onChange={e => setPickup(e.target.value)} fullWidth margin="normal" size="small" />
        <TextField label="Dropoff" value={dropoff} onChange={e => setDropoff(e.target.value)} fullWidth margin="normal" size="small" />
        <Button variant="contained" onClick={bookRide} sx={{ mt: 1, mb: 2 }} fullWidth>Book Ride</Button>
        <PaymentSection />
        <Divider sx={{ my: 2 }} />
        <Typography variant="h6">Profile Settings</Typography>
        <TextField label="New Password" type="password" value={newPassword} onChange={e => setNewPassword(e.target.value)} fullWidth margin="normal" size="small" />
        <Button variant="outlined" onClick={handlePasswordChange} sx={{ mb: 2 }} fullWidth>Change Password</Button>
        {profileMsg && <NotificationBar message={profileMsg} severity={profileMsg.includes('success') ? 'success' : 'info'} onClose={() => setProfileMsg('')} />}
        <Divider sx={{ my: 2 }} />
        <Typography variant="h6" sx={{ mt: 3 }}>Your Bookings</Typography>
        <List>
          {bookings.map((b) => (
            <ListItem key={b.id} divider sx={{ flexDirection: 'column', alignItems: 'flex-start' }}>
              <ListItemText primary={`${b.pickup} 192 ${b.dropoff}`} secondary={`Status: ${b.status} | ${new Date(b.timestamp).toLocaleString()}`} />
            </ListItem>
          ))}
        </List>
      </Paper>
    </Box>
  );
};

export default UserDashboard;
