<!-- Use this file to provide workspace-specific custom instructions to <PERSON><PERSON><PERSON>. For more details, visit https://code.visualstudio.com/docs/copilot/copilot-customization#_use-a-githubcopilotinstructionsmd-file -->

This workspace is a taxi booking application (like RedTaxi) with a Python FastAPI backend, React frontend, SQL database, and Gemini API integration. Prioritize best practices for API, authentication, and frontend-backend communication.
