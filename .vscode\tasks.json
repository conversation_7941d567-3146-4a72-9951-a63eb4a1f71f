{"version": "2.0.0", "tasks": [{"label": "<PERSON>end (FastAPI)", "type": "shell", "command": ".\\venv\\Scripts\\python.exe -m uvicorn main:app --reload", "options": {"cwd": "${workspaceFolder}/backend"}, "problemMatcher": []}, {"label": "Run Frontend (Vite)", "type": "shell", "command": "npm run dev", "options": {"cwd": "${workspaceFolder}/frontend"}, "problemMatcher": []}, {"label": "Run Full Stack", "dependsOn": ["<PERSON>end (FastAPI)", "Run Frontend (Vite)"], "dependsOrder": "parallel"}]}