# Taxi Booking Application (RedTaxi Clone)

This project is a taxi booking application inspired by RedTaxi, built with:
- **Backend:** Python (FastAPI), SQL (SQLAlchemy)
- **Frontend:** React (Vite, JavaScript)
- **API Integration:** Gemini API

## Getting Started

### Backend
1. Navigate to the `backend` folder.
2. Use the Python virtual environment in `venv`.
3. Run the FastAPI server:
   ```powershell
   .\venv\Scripts\python.exe -m uvicorn main:app --reload
   ```

### Frontend
1. Navigate to the `frontend` folder.
2. Start the React development server:
   ```powershell
   npm run dev
   ```

## Project Structure
- `backend/` - Python FastAPI backend
- `frontend/` - React frontend
- `.github/copilot-instructions.md` - Copilot workspace instructions

## Next Steps
- Implement backend endpoints for booking, authentication, and driver management.
- Connect frontend to backend APIs.
- Integrate Gemini API in backend as needed.

---
Replace this README as the project evolves.
