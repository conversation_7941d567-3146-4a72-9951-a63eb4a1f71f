import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { API_URL } from './api';

const AdminAuth: React.FC<{ onLogin: (token: string) => void }> = ({ onLogin }) => {
  const [authPage, setAuthPage] = useState<'login' | 'register'>('login');
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [message, setMessage] = useState('');
  const navigate = useNavigate();

  const register = async () => {
    setMessage('');
    try {
      const res = await fetch(`${API_URL}/admin/register`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ username, password })
      });
      const data = await res.json();

      if (res.ok && data.msg && data.msg.includes('success')) {
        setMessage('Registration successful! Logging you in...');
        // Automatically log in the admin after successful registration
        await login();
      } else {
        setMessage(data.detail || data.msg || 'Registration failed');
      }
    } catch (error) {
      setMessage('Network error. Please try again.');
      console.error('Admin registration error:', error);
    }
  };

  const login = async () => {
    setMessage('');
    try {
      const params = new URLSearchParams();
      params.append('username', username);
      params.append('password', password);
      const res = await fetch(`${API_URL}/admin/token`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        body: params
      });
      const data = await res.json();

      if (res.ok && data.access_token) {
        setMessage('Login successful!');
        onLogin(data.access_token);
        // Navigation is handled by the onLogin callback in App.tsx
      } else {
        setMessage(data.detail || 'Login failed');
      }
    } catch (error) {
      setMessage('Network error. Please try again.');
      console.error('Admin login error:', error);
    }
  };

  return (
    <div className="panel">
      <h2>Admin Login / Register</h2>
      <div className="auth-toggle">
        <button onClick={() => setAuthPage('login')} disabled={authPage === 'login'}>Login</button>
        <button onClick={() => setAuthPage('register')} disabled={authPage === 'register'}>Register</button>
      </div>
      {authPage === 'register' && (
        <div>
          <h3>Register</h3>
          <input placeholder="Admin Username" value={username} onChange={e => setUsername(e.target.value)} />
          <input placeholder="Admin Password" type="password" value={password} onChange={e => setPassword(e.target.value)} />
          <button onClick={register}>Register Admin</button>
        </div>
      )}
      {authPage === 'login' && (
        <div>
          <h3>Login</h3>
          <input placeholder="Admin Username" value={username} onChange={e => setUsername(e.target.value)} />
          <input placeholder="Admin Password" type="password" value={password} onChange={e => setPassword(e.target.value)} />
          <button onClick={login}>Login Admin</button>
        </div>
      )}
      {message && <p className="message">{message}</p>}
    </div>
  );
};

export default AdminAuth;
