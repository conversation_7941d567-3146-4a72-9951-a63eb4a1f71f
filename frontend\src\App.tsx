import { Routes, Route, Navigate, useNavigate } from 'react-router-dom';
import { useState, useEffect } from 'react';
import UserDashboard from './UserDashboard';
import DriverDashboard from './DriverDashboard';
import AdminDashboard from './AdminDashboard';
import UserAuth from './UserAuth';
import DriverAuth from './DriverAuth';
import AdminAuth from './AdminAuth';
import './App.css';

function App() {
  const [userToken, setUserToken] = useState('');
  const [driverToken, setDriverToken] = useState('');
  const [adminToken, setAdminToken] = useState('');
  const navigate = useNavigate();

  // Check localStorage for existing tokens on app load
  useEffect(() => {
    const savedUserToken = localStorage.getItem('userToken');
    const savedDriverToken = localStorage.getItem('driverToken');
    const savedAdminToken = localStorage.getItem('adminToken');

    if (savedUserToken) setUserToken(savedUserToken);
    if (savedDriverToken) setDriverToken(savedDriverToken);
    if (savedAdminToken) setAdminToken(savedAdminToken);
  }, []);

  // Login handlers to pass to auth components
  const handleUserLogin = (token: string) => {
    setUserToken(token);
    localStorage.setItem('userToken', token);
    navigate('/user');
  };
  const handleDriverLogin = (token: string) => {
    setDriverToken(token);
    localStorage.setItem('driverToken', token);
    navigate('/driver');
  };
  const handleAdminLogin = (token: string) => {
    setAdminToken(token);
    localStorage.setItem('adminToken', token);
    navigate('/admin');
  };

  // Logout handlers
  const handleUserLogout = () => {
    setUserToken('');
    localStorage.removeItem('userToken');
    navigate('/user/login');
  };

  const handleDriverLogout = () => {
    setDriverToken('');
    localStorage.removeItem('driverToken');
    navigate('/driver/login');
  };

  const handleAdminLogout = () => {
    setAdminToken('');
    localStorage.removeItem('adminToken');
    navigate('/admin/login');
  };

  return (
    <Routes>
      <Route path="/user" element={userToken ? <UserDashboard onLogout={handleUserLogout} /> : <Navigate to="/user/login" />} />
      <Route path="/user/login" element={<UserAuth onLogin={handleUserLogin} />} />
      <Route path="/driver" element={driverToken ? <DriverDashboard /> : <Navigate to="/driver/login" />} />
      <Route path="/driver/login" element={<DriverAuth onLogin={handleDriverLogin} />} />
      <Route path="/admin" element={adminToken ? <AdminDashboard /> : <Navigate to="/admin/login" />} />
      <Route path="/admin/login" element={<AdminAuth onLogin={handleAdminLogin} />} />
      <Route path="/" element={<div className="landing">
        <h1>Welcome to RedTaxi Clone</h1>
        <div className="landing-nav">
          <a href="/user/login"><button>User Login</button></a>
          <a href="/driver/login"><button>Driver Login</button></a>
          <a href="/admin/login"><button>Admin Login</button></a>
        </div>
      </div>} />
    </Routes>
  );
}

export default App;
