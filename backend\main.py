import uvicorn
from pydantic import BaseModel
from fastapi import <PERSON><PERSON><PERSON>, Depends, HTTPException, status, Body
from fastapi.middleware.cors import CORSMiddleware
from fastapi.security import OAuth2P<PERSON><PERSON><PERSON><PERSON>er, OAuth2PasswordRequestForm
from sqlalchemy.orm import Session
from jose import JWTError, jwt
from datetime import timedelta

from models import User, Booking, Driver, Admin, SessionLocal, Vehicle
from auth_utils import verify_password, get_password_hash, create_access_token
from gemini_utils import call_gemini_api
import os
import stripe
from fastapi import Request

app = FastAPI()

# Allow frontend to communicate with backend
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Dependency to get DB session
def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

oauth2_scheme = OAuth2PasswordBearer(tokenUrl="/token")

@app.get("/")
def read_root():
    return {"message": "Taxi Booking API is running!"}

class RegisterRequest(BaseModel):
    username: str
    password: str

# User registration endpoint
@app.post("/register")
def register(data: RegisterRequest, db: Session = Depends(get_db)):
    username = data.username
    password = data.password
    print(f"[DEBUG] Registering user: {username}")
    user = db.query(User).filter(User.username == username).first()
    if user:
        print("[DEBUG] Username already registered")
        raise HTTPException(status_code=400, detail="Username already registered")
    hashed_password = get_password_hash(password)
    print(f"[DEBUG] Hashed password: {hashed_password}")
    new_user = User(username=username, hashed_password=hashed_password)
    db.add(new_user)
    db.commit()
    db.refresh(new_user)
    print(f"[DEBUG] User registered with ID: {new_user.id}")
    return {"msg": "User registered successfully"}

# Token endpoint (login)
@app.post("/token")
def login(form_data: OAuth2PasswordRequestForm = Depends(), db: Session = Depends(get_db)):
    print(f"[DEBUG] Login attempt for user: {form_data.username}")
    user = db.query(User).filter(User.username == form_data.username).first()
    if not user:
        print("[DEBUG] User not found")
    if not user or not verify_password(form_data.password, user.hashed_password):
        print("[DEBUG] Incorrect username or password")
        raise HTTPException(status_code=401, detail="Incorrect username or password")
    print(f"[DEBUG] User login successful: {user.username}")
    access_token = create_access_token(
        data={"sub": user.username},
        expires_delta=timedelta(minutes=60)
    )
    return {"access_token": access_token, "token_type": "bearer"}

# Get current user from token
def get_current_user(token: str = Depends(oauth2_scheme), db: Session = Depends(get_db)):
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    try:
        payload = jwt.decode(token, "your_secret_key", algorithms=["HS256"])
        username: str = payload.get("sub")
        if username is None:
            raise credentials_exception
    except JWTError:
        raise credentials_exception
    user = db.query(User).filter(User.username == username).first()
    if user is None:
        raise credentials_exception
    return user

# Get current admin from token
def get_current_admin(token: str = Depends(oauth2_scheme), db: Session = Depends(get_db)):
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate admin credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    try:
        payload = jwt.decode(token, "your_secret_key", algorithms=["HS256"])
        username: str = payload.get("sub")
        role: str = payload.get("role")
        if username is None or role != "admin":
            raise credentials_exception
    except JWTError:
        raise credentials_exception
    admin = db.query(Admin).filter(Admin.username == username).first()
    if admin is None:
        raise credentials_exception
    return admin

# Driver registration endpoint (admin only)
@app.post("/driver/register")
def register_driver(data: RegisterRequest, current_admin: Admin = Depends(get_current_admin), db: Session = Depends(get_db)):
    username = data.username
    password = data.password
    print(f"[DEBUG] Registering driver: {username}")
    driver = db.query(Driver).filter(Driver.username == username).first()
    if driver:
        print("[DEBUG] Driver username already registered")
        raise HTTPException(status_code=400, detail="Driver username already registered")
    hashed_password = get_password_hash(password)
    print(f"[DEBUG] Hashed password: {hashed_password}")
    new_driver = Driver(username=username, hashed_password=hashed_password)
    db.add(new_driver)
    db.commit()
    db.refresh(new_driver)
    print(f"[DEBUG] Driver registered with ID: {new_driver.id}")
    return {"msg": "Driver registered successfully"}

# Admin registration endpoint
@app.post("/admin/register")
def register_admin(data: RegisterRequest, db: Session = Depends(get_db)):
    username = data.username
    password = data.password
    print(f"[DEBUG] Registering admin: {username}")
    admin_count = db.query(Admin).count()
    print(f"[DEBUG] Current admin count: {admin_count}")
    if admin_count >= 2:
        print("[DEBUG] Max admins reached")
        raise HTTPException(status_code=400, detail="Maximum number of admins (2) already registered")
    admin = db.query(Admin).filter(Admin.username == username).first()
    if admin:
        print("[DEBUG] Admin username already registered")
        raise HTTPException(status_code=400, detail="Admin username already registered")
    hashed_password = get_password_hash(password)
    print(f"[DEBUG] Hashed password: {hashed_password}")
    new_admin = Admin(username=username, hashed_password=hashed_password)
    db.add(new_admin)
    db.commit()
    db.refresh(new_admin)
    print(f"[DEBUG] Admin registered with ID: {new_admin.id}")
    return {"msg": "Admin registered successfully"}

# Admin login endpoint
@app.post("/admin/token")
def login_admin(form_data: OAuth2PasswordRequestForm = Depends(), db: Session = Depends(get_db)):
    print(f"[DEBUG] Login attempt for admin: {form_data.username}")
    admin = db.query(Admin).filter(Admin.username == form_data.username).first()
    if not admin:
        print("[DEBUG] Admin not found")
    if not admin or not verify_password(form_data.password, admin.hashed_password):
        print("[DEBUG] Incorrect admin username or password")
        raise HTTPException(status_code=401, detail="Incorrect admin username or password")
    print(f"[DEBUG] Admin login successful: {admin.username}")
    access_token = create_access_token(
        data={"sub": admin.username, "role": "admin"},
        expires_delta=timedelta(minutes=60)
    )
    return {"access_token": access_token, "token_type": "bearer"}

# Driver login endpoint
@app.post("/driver/token")
def login_driver(form_data: OAuth2PasswordRequestForm = Depends(), db: Session = Depends(get_db)):
    print(f"[DEBUG] Login attempt for driver: {form_data.username}")
    driver = db.query(Driver).filter(Driver.username == form_data.username).first()
    if not driver:
        print("[DEBUG] Driver not found")
    if not driver or not verify_password(form_data.password, driver.hashed_password):
        print("[DEBUG] Incorrect driver username or password")
        raise HTTPException(status_code=401, detail="Incorrect driver username or password")
    print(f"[DEBUG] Driver login successful: {driver.username}")
    access_token = create_access_token(
        data={"sub": driver.username, "role": "driver"},
        expires_delta=timedelta(minutes=60)
    )
    return {"access_token": access_token, "token_type": "bearer"}

# Booking endpoint
@app.post("/book")
def book_ride(pickup: str, dropoff: str, current_user: User = Depends(get_current_user), db: Session = Depends(get_db)):
    booking = Booking(user_id=current_user.id, pickup=pickup, dropoff=dropoff)
    db.add(booking)
    db.commit()
    db.refresh(booking)
    return {"msg": "Booking successful", "booking_id": booking.id}

# Get bookings for current user
@app.get("/bookings")
def get_bookings(current_user: User = Depends(get_current_user), db: Session = Depends(get_db)):
    bookings = db.query(Booking).filter(Booking.user_id == current_user.id).all()
    return [{"id": b.id, "pickup": b.pickup, "dropoff": b.dropoff, "timestamp": b.timestamp} for b in bookings]

# Example Gemini API integration: enrich booking with Gemini
@app.post("/book_with_gemini")
def book_ride_with_gemini(pickup: str, dropoff: str, current_user: User = Depends(get_current_user), db: Session = Depends(get_db)):
    # Call Gemini API (replace payload as needed)
    gemini_payload = {"pickup": pickup, "dropoff": dropoff, "user": current_user.username}
    gemini_response = call_gemini_api(gemini_payload)
    # Proceed with booking as before
    booking = Booking(user_id=current_user.id, pickup=pickup, dropoff=dropoff)
    db.add(booking)
    db.commit()
    db.refresh(booking)
    return {
        "msg": "Booking successful (Gemini enriched)",
        "booking_id": booking.id,
        "gemini": gemini_response
    }

# List all drivers (admin)
@app.get("/admin/drivers")
def list_drivers(current_admin: Admin = Depends(get_current_admin), db: Session = Depends(get_db)):
    drivers = db.query(Driver).all()
    return [{"id": d.id, "username": d.username} for d in drivers]

# List all users (admin)
@app.get("/admin/users")
def list_users(current_admin: Admin = Depends(get_current_admin), db: Session = Depends(get_db)):
    users = db.query(User).all()
    return [{"id": u.id, "username": u.username} for u in users]

# List all bookings (admin)
@app.get("/admin/bookings")
def list_all_bookings(current_admin: Admin = Depends(get_current_admin), db: Session = Depends(get_db)):
    bookings = db.query(Booking).all()
    return [
        {"id": b.id, "user_id": b.user_id, "driver_id": b.driver_id, "pickup": b.pickup, "dropoff": b.dropoff, "status": b.status, "timestamp": b.timestamp}
        for b in bookings
    ]

# Assign driver to booking (admin)
@app.post("/admin/assign_driver")
def assign_driver(booking_id: int, driver_id: int, current_admin: Admin = Depends(get_current_admin), db: Session = Depends(get_db)):
    booking = db.query(Booking).filter(Booking.id == booking_id).first()
    driver = db.query(Driver).filter(Driver.id == driver_id).first()
    if not booking or not driver:
        raise HTTPException(status_code=404, detail="Booking or driver not found")
    booking.driver_id = driver_id
    booking.status = "assigned"
    db.commit()
    return {"msg": "Driver assigned to booking"}

# Update booking status (driver)
@app.post("/driver/complete_booking")
def complete_booking(booking_id: int, db: Session = Depends(get_db)):
    booking = db.query(Booking).filter(Booking.id == booking_id).first()
    if not booking:
        raise HTTPException(status_code=404, detail="Booking not found")
    booking.status = "completed"
    db.commit()
    return {"msg": "Booking marked as completed"}

@app.put("/user/profile")
def update_user_profile(new_password: str = Body(...), current_user: User = Depends(get_current_user), db: Session = Depends(get_db)):
    current_user.hashed_password = get_password_hash(new_password)
    db.commit()
    return {"msg": "Password updated successfully"}

@app.put("/driver/profile")
def update_driver_profile(new_password: str = Body(...), token: str = Depends(oauth2_scheme), db: Session = Depends(get_db)):
    # Get driver from token
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    try:
        payload = jwt.decode(token, "your_secret_key", algorithms=["HS256"])
        username: str = payload.get("sub")
        role: str = payload.get("role")
        if username is None or role != "driver":
            raise credentials_exception
    except JWTError:
        raise credentials_exception
    driver = db.query(Driver).filter(Driver.username == username).first()
    if driver is None:
        raise credentials_exception
    driver.hashed_password = get_password_hash(new_password)
    db.commit()
    return {"msg": "Password updated successfully"}

# Admin: register a new vehicle
@app.post("/admin/vehicle/register")
def register_vehicle(license_plate: str, model: str, color: str, current_admin: Admin = Depends(get_current_admin), db: Session = Depends(get_db)):
    if db.query(Vehicle).filter(Vehicle.license_plate == license_plate).first():
        raise HTTPException(status_code=400, detail="Vehicle with this license plate already exists")
    vehicle = Vehicle(license_plate=license_plate, model=model, color=color)
    db.add(vehicle)
    db.commit()
    db.refresh(vehicle)
    return {"msg": "Vehicle registered successfully", "vehicle_id": vehicle.id}

# Admin: list all vehicles
@app.get("/admin/vehicles")
def list_vehicles(current_admin: Admin = Depends(get_current_admin), db: Session = Depends(get_db)):
    vehicles = db.query(Vehicle).all()
    return [
        {"id": v.id, "license_plate": v.license_plate, "model": v.model, "color": v.color, "driver_id": v.driver_id}
        for v in vehicles
    ]

# Admin: assign driver to vehicle
@app.post("/admin/vehicle/assign_driver")
def assign_driver_to_vehicle(vehicle_id: int, driver_id: int, current_admin: Admin = Depends(get_current_admin), db: Session = Depends(get_db)):
    vehicle = db.query(Vehicle).filter(Vehicle.id == vehicle_id).first()
    driver = db.query(Driver).filter(Driver.id == driver_id).first()
    if not vehicle or not driver:
        raise HTTPException(status_code=404, detail="Vehicle or driver not found")
    vehicle.driver_id = driver_id
    db.commit()
    return {"msg": "Driver assigned to vehicle"}

# Admin analytics endpoint
@app.get("/admin/analytics")
def admin_analytics(current_admin: Admin = Depends(get_current_admin), db: Session = Depends(get_db)):
    total_users = db.query(User).count()
    total_drivers = db.query(Driver).count()
    total_bookings = db.query(Booking).count()
    completed_rides = db.query(Booking).filter(Booking.status == "completed").count()
    total_vehicles = db.query(Vehicle).count() if 'Vehicle' in globals() else 0
    return {
        "total_users": total_users,
        "total_drivers": total_drivers,
        "total_bookings": total_bookings,
        "completed_rides": completed_rides,
        "total_vehicles": total_vehicles
    }

# In-memory store for FCM tokens (for demo; use DB in production)
fcm_tokens = set()

@app.post("/register-fcm-token")
def register_fcm_token(token: str = Body(...)):
    if not token:
        raise HTTPException(status_code=400, detail="No token provided")
    fcm_tokens.add(token)
    return {"msg": "FCM token registered"}

stripe.api_key = os.getenv("STRIPE_SECRET_KEY", "sk_test_placeholder")

@app.post("/create-payment-intent")
async def create_payment_intent(request: Request):
    data = await request.json()
    amount = int(data.get("amount", 1000))  # amount in cents
    currency = data.get("currency", "usd")
    try:
        intent = stripe.PaymentIntent.create(
            amount=amount,
            currency=currency,
            automatic_payment_methods={"enabled": True},
        )
        return {"clientSecret": intent["client_secret"]}
    except Exception as e:
        return {"error": str(e)}
    

# Add more endpoints for authentication, etc.

if __name__ == "__main__":
    uvicorn.run("main:app", host="0.0.0.0", port=8000, reload=True)
