#!/usr/bin/env python3
"""
Test script to verify backend functionality
"""
import requests
import json
import time

API_URL = "http://localhost:8000"

def test_basic_endpoint():
    """Test if backend is responding"""
    try:
        response = requests.get(f"{API_URL}/", timeout=5)
        print(f"✓ Backend is running: {response.status_code}")
        print(f"  Response: {response.json()}")
        return True
    except Exception as e:
        print(f"✗ Backend not responding: {e}")
        return False

def test_user_registration():
    """Test user registration"""
    try:
        response = requests.post(f"{API_URL}/register", 
            headers={'Content-Type': 'application/json'},
            json={'username': 'testuser', 'password': 'testpass'},
            timeout=5)
        print(f"✓ User registration: {response.status_code}")
        print(f"  Response: {response.json()}")
        return response.status_code == 200
    except Exception as e:
        print(f"✗ User registration failed: {e}")
        return False

def test_user_login():
    """Test user login"""
    try:
        response = requests.post(f"{API_URL}/token",
            headers={'Content-Type': 'application/x-www-form-urlencoded'},
            data={'username': 'testuser', 'password': 'testpass'},
            timeout=5)
        print(f"✓ User login: {response.status_code}")
        result = response.json()
        print(f"  Response: {result}")
        return 'access_token' in result
    except Exception as e:
        print(f"✗ User login failed: {e}")
        return False

def test_admin_registration():
    """Test admin registration"""
    try:
        response = requests.post(f"{API_URL}/admin/register", 
            headers={'Content-Type': 'application/json'},
            json={'username': 'testadmin', 'password': 'testadmin'},
            timeout=5)
        print(f"✓ Admin registration: {response.status_code}")
        print(f"  Response: {response.json()}")
        return response.status_code == 200
    except Exception as e:
        print(f"✗ Admin registration failed: {e}")
        return False

def test_admin_login():
    """Test admin login"""
    try:
        response = requests.post(f"{API_URL}/admin/token",
            headers={'Content-Type': 'application/x-www-form-urlencoded'},
            data={'username': 'testadmin', 'password': 'testadmin'},
            timeout=5)
        print(f"✓ Admin login: {response.status_code}")
        result = response.json()
        print(f"  Response: {result}")
        return 'access_token' in result
    except Exception as e:
        print(f"✗ Admin login failed: {e}")
        return False

if __name__ == "__main__":
    print("Testing Backend Functionality")
    print("=" * 40)
    
    # Test basic connectivity
    if not test_basic_endpoint():
        print("\n❌ Backend is not running. Please start the backend first.")
        exit(1)
    
    print("\n" + "=" * 40)
    print("Testing Registration and Login")
    print("=" * 40)
    
    # Test user flows
    test_user_registration()
    test_user_login()
    
    # Test admin flows
    test_admin_registration()
    test_admin_login()
    
    print("\n" + "=" * 40)
    print("✅ Backend testing completed!")
